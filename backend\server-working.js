const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// Estado do WhatsApp
let whatsappState = {
  isConnected: false,
  sessionName: 'vereadora-rafaela',
  status: 'initializing',
  connectionAttempts: 0,
  timestamp: new Date().toISOString(),
  qrCode: null,
  client: null,
  syncStatus: {
    isSync: false,
    totalChats: 0,
    syncedChats: 0,
    currentChat: null,
    startTime: null,
    endTime: null
  }
};

// Função para gerar QR code simulado
function generateSimulatedQRCode() {
  const timestamp = new Date().toISOString();
  const attempt = whatsappState.connectionAttempts + 1;
  
  // QR Code base64 simulado mais realista
  const simulatedQRBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  
  whatsappState.qrCode = {
    base64: simulatedQRBase64,
    ascii: '█▀▀▀▀▀█ ▀▀█▀▀ █▀▀▀▀▀█\n█ ███ █ ▀█▀█▀ █ ███ █\n█ ▀▀▀ █ █▀▀▀█ █ ▀▀▀ █\n▀▀▀▀▀▀▀ ▀ ▀ ▀ ▀▀▀▀▀▀▀',
    path: `/tmp/qr-${Date.now()}.png`,
    timestamp: timestamp,
    attempt: attempt
  };
  
  whatsappState.connectionAttempts = attempt;
  whatsappState.status = 'waiting_for_qr_scan';
  whatsappState.timestamp = timestamp;
  
  console.log(`📱 QR Code simulado gerado - Tentativa ${attempt}`);
  
  // Simular expiração do QR code após 30 segundos
  setTimeout(() => {
    if (whatsappState.qrCode && whatsappState.qrCode.attempt === attempt) {
      generateSimulatedQRCode(); // Gerar novo QR code
    }
  }, 30000);
}

// Simular conexão após escanear QR
function simulateConnection() {
  setTimeout(() => {
    whatsappState.isConnected = true;
    whatsappState.status = 'connected';
    whatsappState.qrCode = null;
    whatsappState.timestamp = new Date().toISOString();
    
    console.log('🎉 Conexão simulada estabelecida!');
    
    // Simular sincronização
    setTimeout(() => {
      simulateSync();
    }, 2000);
  }, 45000); // Simular conexão após 45 segundos
}

// Simular sincronização de conversas
function simulateSync() {
  whatsappState.syncStatus.isSync = true;
  whatsappState.syncStatus.startTime = new Date().toISOString();
  whatsappState.syncStatus.totalChats = 10;
  whatsappState.syncStatus.syncedChats = 0;
  
  console.log('🔄 Iniciando sincronização simulada...');
  
  const chats = [
    'João Silva', 'Maria Santos', 'Pedro Oliveira', 'Ana Costa',
    'Carlos Lima', 'Lucia Ferreira', 'Roberto Alves', 'Fernanda Rocha',
    'Marcos Pereira', 'Juliana Souza'
  ];
  
  let currentIndex = 0;
  const syncInterval = setInterval(() => {
    if (currentIndex < chats.length) {
      whatsappState.syncStatus.currentChat = chats[currentIndex];
      whatsappState.syncStatus.syncedChats = currentIndex + 1;
      
      console.log(`📥 Sincronizando: ${chats[currentIndex]} (${currentIndex + 1}/${chats.length})`);
      currentIndex++;
    } else {
      clearInterval(syncInterval);
      whatsappState.syncStatus.isSync = false;
      whatsappState.syncStatus.endTime = new Date().toISOString();
      whatsappState.syncStatus.currentChat = null;
      
      console.log('✅ Sincronização simulada concluída!');
    }
  }, 2000);
}

// Middlewares
app.use(cors());
app.use(express.json());

// Servir arquivos estáticos (QR codes)
app.use('/qr', express.static(path.join(__dirname, 'data')));

// Basic routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'Vereadora Rafaela Backend',
    port: PORT,
    whatsapp: {
      status: whatsappState.status,
      connected: whatsappState.isConnected,
      hasQR: !!whatsappState.qrCode
    }
  });
});

// WhatsApp Status Route
app.get('/api/whatsapp/status', (req, res) => {
  console.log('📱 Status solicitado:', {
    status: whatsappState.status,
    connected: whatsappState.isConnected,
    hasQR: !!whatsappState.qrCode,
    attempts: whatsappState.connectionAttempts,
    syncStatus: whatsappState.syncStatus
  });
  
  res.json({ 
    success: true,
    data: {
      ...whatsappState,
      serviceType: 'simulated',
      isSimulator: true,
      isReal: false,
      syncStatus: whatsappState.syncStatus
    },
    message: 'Status obtido com sucesso'
  });
});

// WhatsApp QR Code Route
app.get('/api/whatsapp/qr', (req, res) => {
  console.log('📱 QR Code solicitado');
  
  if (!whatsappState.qrCode) {
    return res.json({ 
      success: false,
      data: {
        qrCode: null
      },
      message: 'QR Code não disponível. Aguarde a inicialização.'
    });
  }
  
  res.json({ 
    success: true,
    data: {
      qrCode: whatsappState.qrCode
    },
    message: 'QR Code obtido com sucesso'
  });
});

// WhatsApp Restart Route
app.post('/api/whatsapp/restart', async (req, res) => {
  console.log('📱 Reiniciando conexão WhatsApp...');
  
  // Resetar estado
  whatsappState.isConnected = false;
  whatsappState.status = 'restarting';
  whatsappState.connectionAttempts = 0;
  whatsappState.qrCode = null;
  whatsappState.client = null;
  whatsappState.timestamp = new Date().toISOString();
  
  // Reinicializar
  setTimeout(() => {
    generateSimulatedQRCode();
    simulateConnection();
  }, 2000);
  
  res.json({ 
    success: true,
    message: 'Reinicialização iniciada'
  });
});

// Simular conexão manual
app.post('/api/whatsapp/simulate-connect', (req, res) => {
  console.log('📱 Simulando conexão manual...');
  
  whatsappState.isConnected = true;
  whatsappState.status = 'connected';
  whatsappState.qrCode = null;
  whatsappState.timestamp = new Date().toISOString();
  
  // Iniciar sincronização
  setTimeout(() => {
    simulateSync();
  }, 1000);
  
  res.json({ 
    success: true,
    message: 'Conexão simulada com sucesso'
  });
});

// Obter status da sincronização
app.get('/api/whatsapp/sync/status', (req, res) => {
  res.json({
    success: true,
    data: whatsappState.syncStatus,
    message: 'Status da sincronização obtido com sucesso'
  });
});

// Inicializar com QR code
setTimeout(() => {
  generateSimulatedQRCode();
  simulateConnection();
}, 2000);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Backend rodando em http://localhost:${PORT}`);
  console.log(`📱 WhatsApp API: http://localhost:${PORT}/api/whatsapp/status`);
  console.log(`🔗 QR Code API: http://localhost:${PORT}/api/whatsapp/qr`);
  console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🔄 Restart API: http://localhost:${PORT}/api/whatsapp/restart`);
  console.log(`🎭 Simulate Connect: http://localhost:${PORT}/api/whatsapp/simulate-connect`);
  console.log(`📊 Sync Status: http://localhost:${PORT}/api/whatsapp/sync/status`);
  console.log(`📁 QR Files: http://localhost:${PORT}/qr/`);
  console.log('');
  console.log('🔄 Inicializando modo simulado em 2 segundos...');
  console.log('📱 Sincronização automática será ativada após conexão via QR');
  console.log('');
  console.log('💡 Para testar: use /api/whatsapp/simulate-connect para simular conexão manual');
  console.log('📱 QR Code será exibido no frontend e renovado automaticamente');
  console.log('🎯 Após 45 segundos, a conexão será simulada automaticamente');
});
