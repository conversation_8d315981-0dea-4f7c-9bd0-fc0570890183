import React, { useRef, useState } from 'react';
import { documentProcessor } from '../services/documentProcessor';

interface DocumentInputProps {
  onUpload: (file: File) => void;
}

export const DocumentInput: React.FC<DocumentInputProps> = ({ onUpload }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    setIsValidating(true);

    try {
      // Validar arquivo
      const validation = documentProcessor.validateFile(file);
      if (!validation.valid) {
        alert(validation.error);
        return;
      }

      // Estimar tempo de processamento
      const estimatedTime = documentProcessor.estimateProcessingTime(file);
      const timeInSeconds = Math.ceil(estimatedTime / 1000);

      const confirmed = confirm(
        `Arquivo: ${file.name}\n` +
        `Tamanho: ${(file.size / 1024 / 1024).toFixed(2)} MB\n` +
        `Tempo estimado: ~${timeInSeconds}s\n\n` +
        `Deseja continuar com o upload?`
      );

      if (confirmed) {
        onUpload(file);
      }
    } catch (error) {
      console.error('Erro na validação:', error);
      alert('Erro ao validar arquivo. Tente novamente.');
    } finally {
      setIsValidating(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="w-full">
      <div
        className={`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
          ${isDragging
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-blue-500 hover:bg-gray-50'
          }
          ${isValidating ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={!isValidating ? handleClick : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf,.txt,.md,.doc,.docx"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          disabled={isValidating}
        />

        <div className="flex flex-col items-center space-y-4">
          {isValidating ? (
            <>
              <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <p className="text-gray-800 font-medium text-base">Validando arquivo...</p>
            </>
          ) : (
            <>
              <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-2 shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>

              <div>
                <p className="text-lg font-medium text-gray-800">
                  {isDragging ? 'Solte o arquivo aqui' : 'Carregar documento'}
                </p>
                <p className="text-base text-gray-600 mt-2">
                  Arraste e solte ou clique para selecionar
                </p>
              </div>

              <div className="text-sm text-gray-600 space-y-1">
                <p>PDF, TXT, MD, DOC, DOCX • Máximo 10MB</p>
                <p>O documento será processado automaticamente</p>
              </div>
            </>
          )}
        </div>
      </div>

      <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <p className="text-sm text-blue-800">
          <strong>💡 Dica:</strong> Carregue documentos oficiais da Câmara Municipal, projetos de lei,
          regulamentos ou outros materiais relevantes para melhorar as respostas da IA.
        </p>
      </div>
    </div>
  );
};
