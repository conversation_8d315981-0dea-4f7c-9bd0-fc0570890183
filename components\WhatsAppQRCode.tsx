import React, { useState, useEffect } from 'react';
import { whatsappService } from '../services/whatsappService';

interface QRCodeData {
  base64: string;
  ascii: string;
  path: string;
  timestamp: string;
  attempt?: number;
}

interface WhatsAppQRCodeProps {
  onConnectionChange?: (isConnected: boolean) => void;
}

export const WhatsAppQRCode: React.FC<WhatsAppQRCodeProps> = ({ onConnectionChange }) => {
  const [qrCode, setQrCode] = useState<QRCodeData | null>(null);
  const [status, setStatus] = useState<string>('Carregando...');
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [attempt, setAttempt] = useState(0);
  const [syncStatus, setSyncStatus] = useState<any>(null);

  // Verificar status e QR Code
  const checkStatus = async () => {
    try {
      console.log('🔍 Verificando status WhatsApp...');
      const statusData = await whatsappService.getStatus();
      console.log('📊 Status recebido:', statusData);

      if (statusData) {
        setIsConnected(statusData.isConnected);
        setStatus(statusData.status || 'Desconhecido');
        setSyncStatus(statusData.syncStatus || null);

        // Notificar mudança de conexão
        onConnectionChange?.(statusData.isConnected);

        if (statusData.isConnected) {
          console.log('✅ WhatsApp conectado');
          setQrCode(null);
          setLoading(false);
        } else if (statusData.qrCode) {
          console.log('📱 QR Code encontrado no status:', statusData.qrCode);
          setQrCode(statusData.qrCode);
          setAttempt(statusData.qrCode.attempt || 0);
          setLoading(false);
        } else {
          console.log('🔍 QR Code não encontrado no status, tentando obter separadamente...');
          // Tentar obter QR Code separadamente
          try {
            const qrData = await whatsappService.getQRCode();
            console.log('📱 QR Code obtido separadamente:', qrData);
            if (qrData) {
              setQrCode(qrData);
              setAttempt(qrData.attempt || 0);
            }
          } catch (qrError) {
            console.warn('⚠️ QR Code não disponível ainda:', qrError);
          }
          setLoading(false);
        }
      }

      setError(null);
    } catch (error) {
      console.error('❌ Erro ao verificar status:', error);
      setError('Erro ao conectar com o backend');
      setLoading(false);
    }
  };

  // Reiniciar conexão
  const restartConnection = async () => {
    try {
      setLoading(true);
      setError(null);
      await whatsappService.restart();
      setTimeout(checkStatus, 2000);
    } catch (error) {
      console.error('Erro ao reiniciar:', error);
      setError('Erro ao reiniciar conexão');
      setLoading(false);
    }
  };

  useEffect(() => {
    checkStatus();
    
    // Verificar status a cada 5 segundos
    const interval = setInterval(checkStatus, 5000);
    
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-4 border-green-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-700 mb-2">🔄 Inicializando WhatsApp</h3>
          <p className="text-gray-500">Aguarde enquanto preparamos a conexão...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-red-200 p-6">
        <div className="text-center">
          <div className="text-red-400 text-4xl mb-4">⚠️</div>
          <h3 className="text-lg font-medium text-red-700 mb-2">Erro de Conexão</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={restartConnection}
            className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
          >
            🔄 Tentar Novamente
          </button>
        </div>
      </div>
    );
  }

  if (isConnected) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-green-200 p-6">
        <div className="text-center">
          <div className="text-green-500 text-4xl mb-4">✅</div>
          <h3 className="text-lg font-medium text-green-700 mb-2">WhatsApp Conectado!</h3>
          <p className="text-green-600 mb-4">
            Vereadora Rafaela está online e pronta para atender
          </p>

          {/* Status de Sincronização */}
          {syncStatus && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              {syncStatus.isSync ? (
                <div className="text-blue-800">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    <span className="font-medium">Sincronizando conversas...</span>
                  </div>
                  <div className="text-sm">
                    {syncStatus.currentChat && (
                      <p>📱 {syncStatus.currentChat}</p>
                    )}
                    <p>
                      {syncStatus.syncedChats}/{syncStatus.totalChats} conversas sincronizadas
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-blue-800">
                  <div className="flex items-center justify-center space-x-2">
                    <span className="text-blue-600">✅</span>
                    <span className="font-medium">Conversas sincronizadas!</span>
                  </div>
                  {syncStatus.syncedChats > 0 && (
                    <p className="text-sm mt-1">
                      {syncStatus.syncedChats} conversas carregadas com histórico completo
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          <div className="flex justify-center space-x-3">
            <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
              📱 {status}
            </span>
            <button
              onClick={restartConnection}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
            >
              🔄 Reconectar
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="text-center mb-6">
        <h3 className="text-lg font-medium text-gray-700 mb-2">
          📱 Conectar WhatsApp da Vereadora Rafaela
        </h3>
        <p className="text-gray-500">
          Escaneie o QR Code com o WhatsApp para conectar
        </p>
        {attempt > 0 && (
          <p className="text-orange-600 text-sm mt-1">
            Tentativa {attempt} - QR Code atualizado
          </p>
        )}
      </div>

      {qrCode ? (
        <div className="space-y-4">
          {/* QR Code em Base64 */}
          {qrCode.base64 && (
            <div className="flex justify-center">
              <div className="bg-white p-4 rounded-lg border-2 border-gray-200 shadow-sm">
                <img
                  src={`data:image/png;base64,${qrCode.base64}`}
                  alt="QR Code WhatsApp"
                  className="w-64 h-64 mx-auto"
                />
              </div>
            </div>
          )}

          {/* QR Code ASCII (fallback) */}
          {!qrCode.base64 && qrCode.ascii && (
            <div className="bg-gray-900 text-white p-4 rounded-lg overflow-auto">
              <pre className="text-xs leading-none font-mono whitespace-pre">
                {qrCode.ascii}
              </pre>
            </div>
          )}

          {/* Instruções */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">📋 Como conectar:</h4>
            <ol className="text-sm text-blue-700 space-y-1">
              <li>1. Abra o WhatsApp no celular da Vereadora</li>
              <li>2. Toque nos três pontos (⋮) → "Dispositivos conectados"</li>
              <li>3. Toque em "Conectar um dispositivo"</li>
              <li>4. Escaneie este QR Code</li>
            </ol>
          </div>

          {/* Informações do QR Code */}
          <div className="text-center text-xs text-gray-500 space-y-1">
            <p>🕐 Gerado em: {new Date(qrCode.timestamp).toLocaleString('pt-BR')}</p>
            <p>⏱️ QR Code expira em 20 segundos</p>
          </div>

          {/* Botão para atualizar */}
          <div className="text-center">
            <button
              onClick={checkStatus}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              🔄 Atualizar QR Code
            </button>
          </div>
        </div>
      ) : (
        <div className="text-center">
          <div className="text-gray-400 text-4xl mb-4">📱</div>
          <p className="text-gray-500 mb-4">
            Aguardando geração do QR Code...
          </p>
          <p className="text-sm text-gray-400">
            Status: {status}
          </p>
          <button
            onClick={checkStatus}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            🔄 Verificar Status
          </button>
        </div>
      )}
    </div>
  );
};
