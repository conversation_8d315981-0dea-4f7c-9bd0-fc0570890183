import React, { useState, useEffect } from 'react';
import { whatsappService, WhatsAppStatus as IWhatsAppStatus, WhatsAppStats } from '../services/whatsappService';
import { useAppState } from '../contexts/AppStateContext';

export const WhatsAppStatus: React.FC = () => {
  const { state, actions } = useAppState();
  const [stats, setStats] = useState<WhatsAppStats | null>(null);
  const [showQR, setShowQR] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Carregar estatísticas se conectado
    if (state.whatsapp.isConnected) {
      loadStats();
    }
  }, [state.whatsapp.isConnected]);

  const loadStats = async () => {
    try {
      console.log('🔍 WhatsAppStatus: Carregando estatísticas...');
      const currentStats = await whatsappService.getStats();
      console.log('📊 WhatsAppStatus: Estatísticas recebidas:', currentStats);
      setStats(currentStats);
    } catch (statsError) {
      console.warn('⚠️ WhatsAppStatus: Erro ao carregar estatísticas:', statsError);
    }
  };



  const getStatusIcon = () => {
    if (state.whatsapp.loading) {
      return (
        <div className="loading-modern">
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
          <div className="loading-dot"></div>
        </div>
      );
    }

    if (state.whatsapp.error) {
      return <span className="text-gray-400">⚪</span>;
    }

    if (state.whatsapp.isConnected) {
      return <span className="text-green-500 animate-pulse">✅</span>;
    }

    if (state.whatsapp.qrCode) {
      return <span className="text-orange-500 animate-pulse">📱</span>;
    }

    return <span className="text-red-500">🔴</span>;
  };

  const getStatusText = () => {
    if (state.whatsapp.loading) return 'Verificando...';
    if (state.whatsapp.error) return state.whatsapp.error;

    return state.whatsapp.status;
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="glass-card p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <div>
            <h3 className="font-semibold text-gray-800 flex items-center space-x-2">
              <span>📱 WhatsApp Backend</span>
              {state.whatsapp.isConnected && (
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  Online
                </span>
              )}
            </h3>
            <p className="text-sm text-gray-600">{getStatusText()}</p>
          </div>
        </div>
        
        <div className="flex space-x-2">
          {state.whatsapp.qrCode && !state.whatsapp.isConnected && (
            <button
              onClick={() => setShowQR(!showQR)}
              className="px-3 py-1 text-sm bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              {showQR ? 'Ocultar QR' : 'Mostrar QR'}
            </button>
          )}
          
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="px-3 py-1 text-sm bg-white/30 hover:bg-white/50 rounded-lg transition-colors"
          >
            {showDetails ? 'Ocultar' : 'Detalhes'}
          </button>
          
          {!state.whatsapp.error && (
            <button
              onClick={actions.updateWhatsAppStatus}
              disabled={state.whatsapp.loading}
              className="px-3 py-1 text-sm bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              🔄 Atualizar
            </button>
          )}
        </div>
      </div>

      {/* QR Code */}
      {showQR && state.whatsapp.qrCode && !state.whatsapp.isConnected && (
        <div className="mb-4 p-4 bg-white rounded-lg border-2 border-orange-200">
          <div className="text-center">
            <h4 className="font-medium text-gray-800 mb-3">
              📱 Escaneie o QR Code com WhatsApp
            </h4>
            
            {state.whatsapp.qrCode ? (
              <div className="flex flex-col items-center space-y-3">
                <img
                  src={state.whatsapp.qrCode}
                  alt="QR Code WhatsApp"
                  className="w-48 h-48 border border-gray-200 rounded-lg"
                />
                <p className="text-xs text-gray-500">
                  Última atualização: {state.whatsapp.lastUpdate?.toLocaleString('pt-BR')}
                </p>
                <div className="flex space-x-2">
                  <a
                    href={`${whatsappService.getBackendURL()}${status.qrCode.path}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
                  >
                    🔗 Abrir em Nova Aba
                  </a>
                  <button
                    onClick={() => navigator.clipboard.writeText(status.qrCode?.base64 || '')}
                    className="px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded transition-colors"
                  >
                    📋 Copiar Base64
                  </button>
                </div>
              </div>
            ) : (
              <div className="text-gray-500">
                <div className="loading-modern mb-2">
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                  <div className="loading-dot"></div>
                </div>
                <p>Gerando QR Code...</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Detalhes */}
      {showDetails && (
        <div className="space-y-4 pt-4 border-t border-white/20">
          {/* Informações de Conexão */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-gray-700">Status da Conexão</p>
              <div className="space-y-1 text-xs text-gray-600">
                <p>Sessão: {status?.sessionName || 'N/A'}</p>
                <p>Tentativas: {status?.connectionAttempts || 0}</p>
                <p>Backend: {whatsappService.getBackendURL()}</p>
              </div>
            </div>
            
            {stats && (
              <div>
                <p className="text-sm font-medium text-gray-700">Estatísticas</p>
                <div className="space-y-1 text-xs text-gray-600">
                  <p>Uptime: {formatUptime(stats.uptime)}</p>
                  <p>Sessões ativas: {stats.messages.activeSessions}</p>
                  <p>Total mensagens: {stats.messages.totalMessages}</p>
                </div>
              </div>
            )}
          </div>

          {/* Funcionalidades */}
          <div>
            <p className="text-sm font-medium text-gray-700 mb-2">Funcionalidades WhatsApp:</p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className={`flex items-center space-x-1 ${status?.isConnected ? 'text-green-600' : 'text-gray-400'}`}>
                <span>{status?.isConnected ? '✅' : '❌'}</span>
                <span>Envio de mensagens</span>
              </div>
              <div className={`flex items-center space-x-1 ${status?.isConnected ? 'text-green-600' : 'text-gray-400'}`}>
                <span>{status?.isConnected ? '✅' : '❌'}</span>
                <span>Recebimento automático</span>
              </div>
              <div className={`flex items-center space-x-1 ${stats?.messages.autoReplyEnabled ? 'text-green-600' : 'text-gray-400'}`}>
                <span>{stats?.messages.autoReplyEnabled ? '✅' : '❌'}</span>
                <span>Respostas automáticas</span>
              </div>
              <div className={`flex items-center space-x-1 ${stats?.messages.isBusinessHours ? 'text-green-600' : 'text-orange-500'}`}>
                <span>{stats?.messages.isBusinessHours ? '🟢' : '🟡'}</span>
                <span>Horário comercial</span>
              </div>
            </div>
          </div>

          {/* Ações */}
          <div className="flex space-x-2 pt-3">
            <button
              onClick={actions.updateWhatsAppStatus}
              className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors"
            >
              🔄 Atualizar Status
            </button>
            
            {!backendAvailable && (
              <div className="flex-1 text-xs text-gray-600">
                <p>💡 Para usar WhatsApp:</p>
                <p>1. Vá para a pasta backend/</p>
                <p>2. Execute: npm run dev</p>
                <p>3. O backend rodará na porta 3001</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
