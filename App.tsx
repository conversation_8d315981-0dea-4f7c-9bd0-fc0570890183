import { useState, useRef, useEffect } from 'react';
import { ChatInterface } from './components/ChatInterface';
import { DocumentInput } from './components/DocumentInput';
import { VereadoraHeader } from './components/VereadoraHeader';
import { Sidebar } from './components/Sidebar';
import { whatsappService } from './services/whatsappService';

import { DocumentManager } from './components/DocumentManager';
import { MonitoringDashboard } from './components/MonitoringDashboard';

import { vereadoraRAGService } from './services/vereadoraRAGService';
import { WhatsAppManager } from './components/WhatsAppManager';
import { WhatsAppChats } from './components/WhatsAppChats';
import { databaseService } from './services/databaseService';
import { monitoringService } from './services/monitoringService';
import { isSupabaseConfigured } from './config/supabase';
import { isGeminiConfigured } from './config/gemini';
import type { Message, Document } from './types';

function App() {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);

  const [activeTab, setActiveTab] = useState<'chat' | 'documents' | 'monitoring' | 'whatsapp'>('chat');
  const [whatsappConnected, setWhatsappConnected] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Monitorar status do WhatsApp
  useEffect(() => {
    const checkWhatsAppStatus = async () => {
      try {
        const status = await whatsappService.getStatus();
        console.log('🔍 App: Status WhatsApp recebido:', status);
        console.log('🔍 App: isConnected atual:', whatsappConnected);
        console.log('🔍 App: isConnected novo:', status?.isConnected);

        const newConnectionStatus = status?.isConnected || false;
        if (newConnectionStatus !== whatsappConnected) {
          console.log('🔄 App: Atualizando status WhatsApp:', newConnectionStatus);
          setWhatsappConnected(newConnectionStatus);
        }
      } catch (error) {
        console.error('❌ App: Erro ao verificar status WhatsApp:', error);
        setWhatsappConnected(false);
      }
    };

    // Verificar status inicial
    checkWhatsAppStatus();

    // Polling para atualizações
    const interval = setInterval(checkWhatsAppStatus, 3000); // Reduzir para 3 segundos

    return () => clearInterval(interval);
  }, [whatsappConnected]); // Adicionar dependência para detectar mudanças

  // Verificar status do WhatsApp
  useEffect(() => {
    const checkWhatsAppStatus = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/whatsapp/status');
        const data = await response.json();
        setWhatsappConnected(data.data?.isConnected || false);
      } catch (error) {
        console.error('Erro ao verificar status WhatsApp:', error);
        setWhatsappConnected(false);
      }
    };

    checkWhatsAppStatus();
    // Verificar status a cada 30 segundos
    const interval = setInterval(checkWhatsAppStatus, 30000);

    return () => clearInterval(interval);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Carregar documentos apenas uma vez na inicialização
    loadDocuments();
  }, []); // Array vazio para executar apenas uma vez

  useEffect(() => {
    // Polling mais conservador para atualizar status dos documentos
    const interval = setInterval(() => {
      // Só fazer polling se houver documentos em processamento
      const hasProcessingDocs = documents.some(doc =>
        doc.status === 'processing' || doc.status === 'uploading'
      );

      if (hasProcessingDocs) {
        console.log('🔄 Atualizando status dos documentos...');
        loadDocuments();
      }
    }, 10000); // Aumentado para 10 segundos

    return () => clearInterval(interval);
  }, [documents]);

  const loadDocuments = async () => {
    try {
      // Usar apenas backend - não tentar Supabase diretamente
      const response = await fetch('http://localhost:3002/api/documents');
      if (response.ok) {
        const result = await response.json();
        setDocuments(result.documents || []);
        console.log('✅ Documentos carregados via backend:', result.documents?.length || 0);
      } else {
        console.warn('⚠️ Backend retornou erro:', response.status);
        // Usar lista vazia em caso de erro
        setDocuments([]);
      }
    } catch (error) {
      console.error('❌ Erro ao carregar documentos:', error);
      // Em caso de erro total, usar lista vazia
      setDocuments([]);
    }
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    try {
      // Registrar métricas
      await monitoringService.recordInteraction('message_sent', {
        messageLength: content.length,
        timestamp: new Date().toISOString()
      });

      const response = await vereadoraRAGService.processMessage(content);
      
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.answer,
        sender: 'assistant',
        timestamp: new Date(),
        sources: response.sources,
        confidence: response.confidence
      };

      setMessages(prev => [...prev, botMessage]);

      // Registrar métricas de resposta
      await monitoringService.recordInteraction('message_received', {
        responseLength: response.answer.length,
        confidence: response.confidence,
        sourcesCount: response.sources?.length || 0,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Erro ao processar mensagem:', error);
      
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Desculpe, ocorreu um erro ao processar sua mensagem. Tente novamente.',
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);

      // Registrar erro
      await monitoringService.recordInteraction('error', {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDocumentUpload = async (file: File) => {
    console.log('📄 App: handleDocumentUpload chamado para:', file.name);
    try {
      setIsLoading(true);

      await monitoringService.recordInteraction('document_upload', {
        fileName: file.name,
        fileSize: file.size,
        status: 'start',
        timestamp: new Date().toISOString()
      });

      console.log('📄 App: Chamando vereadoraRAGService.uploadDocument...');
      const document = await vereadoraRAGService.uploadDocument(file);
      console.log('📄 App: Upload concluído:', document);

      console.log('📄 App: Recarregando lista de documentos...');
      await loadDocuments();

      await monitoringService.recordInteraction('document_upload', {
        documentId: document.id,
        fileName: file.name,
        status: 'success',
        timestamp: new Date().toISOString()
      });

      // Adicionar mensagem de confirmação
      const confirmationMessage: Message = {
        id: Date.now().toString(),
        content: `✅ Documento "${file.name}" foi carregado com sucesso! Agora posso responder perguntas sobre seu conteúdo.`,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, confirmationMessage]);
      console.log('📄 App: Mensagem de confirmação adicionada');

    } catch (error) {
      console.error('❌ App: Erro ao fazer upload:', error);

      await monitoringService.recordInteraction('error', {
        fileName: file.name,
        type: 'document_upload',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString()
      });

      const errorMessage: Message = {
        id: Date.now().toString(),
        content: `❌ Erro ao carregar o documento "${file.name}". Tente novamente.`,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, errorMessage]);
      console.log('📄 App: Mensagem de erro adicionada');
    } finally {
      setIsLoading(false);
    }
  };



  const handleDeleteDocument = async (documentId: string) => {
    try {
      await databaseService.deleteDocument(documentId);
      await loadDocuments();
    } catch (error) {
      console.error('Erro ao deletar documento:', error);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <div className="flex flex-col h-full">
            {/* Área do chat expandida */}
            <div className="flex-1 overflow-hidden">
              <ChatInterface
                messages={messages}
                onSendMessage={handleSendMessage}
                isLoading={isLoading}
                messagesEndRef={messagesEndRef}
              />
            </div>
            {/* Área de upload de documentos */}
            <div className="border-t border-gray-200 p-6 bg-white">
              <div className="max-w-5xl mx-auto">
                <DocumentInput onUpload={handleDocumentUpload} />
              </div>
            </div>
          </div>
        );

      case 'documents':
        return (
          <DocumentManager
            documents={documents}
            onDeleteDocument={handleDeleteDocument}
            onUpload={handleDocumentUpload}
          />
        );

      case 'monitoring':
        return <MonitoringDashboard />;
      case 'whatsapp':
        return (
          <div className="space-y-6">
            <WhatsAppManager />
            <WhatsAppChats isConnected={whatsappConnected} />
          </div>
        );
      default:
        return null;
    }
  };

  const isDemoMode = !isSupabaseConfigured || !isGeminiConfigured;

  return (
    <div className="min-h-screen bg-gray-50">
      <VereadoraHeader whatsappConnected={whatsappConnected} />

      {isDemoMode && (
        <div className="max-w-6xl mx-auto px-6 py-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-4">
                <h3 className="text-base font-medium text-yellow-800">
                  Modo Demonstração Ativo
                </h3>
                <div className="mt-2 text-base text-yellow-700">
                  <p className="mb-3">
                    O sistema está funcionando em modo demonstração com todas as funcionalidades ativas!
                  </p>
                  <div className="space-y-2">
                    {!isSupabaseConfigured && (
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                        <span>Configure Supabase para persistência de dados</span>
                      </div>
                    )}
                    {!isGeminiConfigured && (
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                        <span>Configure Gemini para IA avançada</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Layout principal com sidebar à direita */}
      <div className="flex h-[calc(100vh-120px)] relative">
        {/* Conteúdo principal expandido */}
        <div className="flex-1 pr-0 lg:pr-64 transition-all duration-300">
          <div className="h-full bg-gh-bg rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            {renderTabContent()}
          </div>
        </div>

        {/* Sidebar à direita - responsiva */}
        <div className="hidden lg:block">
          <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
        </div>

        {/* Sidebar mobile - bottom navigation */}
        <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
          <div className="flex justify-around py-2">
            {[
              { id: 'chat', icon: '💬', name: 'Chat' },
              { id: 'documents', icon: '📄', name: 'Docs' },
              { id: 'monitoring', icon: '📊', name: 'Monitor' },
              { id: 'whatsapp', icon: '📱', name: 'WhatsApp' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id as any)}
                className={`flex flex-col items-center py-2 px-3 rounded-lg transition-all ${
                  activeTab === tab.id
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                <span className="text-lg mb-1">{tab.icon}</span>
                <span className="text-xs font-medium">{tab.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
