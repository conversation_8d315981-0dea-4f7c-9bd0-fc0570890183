import React, { useState, useEffect } from 'react';
import { monitoringService } from '../services/monitoringService';
import { cacheService } from '../services/cacheService';
import { ConfigurationStatus } from './ConfigurationStatus';
import { WhatsAppStatus } from './WhatsAppStatus';
import { SecurityStatus } from './SecurityStatus';
import { AntiBanMonitor } from './AntiBanMonitor';
import type { MonitoringMetrics } from '../types';

export const MonitoringDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<MonitoringMetrics | null>(null);
  const [realTimeStats, setRealTimeStats] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  useEffect(() => {
    loadMetrics();
    loadRealTimeStats();
    loadCacheStats();

    // Atualizar a cada 30 segundos
    const interval = setInterval(() => {
      loadRealTimeStats();
      loadCacheStats();
      setLastUpdate(new Date());
    }, 30000);

    // Atualizar métricas completas a cada 5 minutos
    const metricsInterval = setInterval(loadMetrics, 5 * 60 * 1000);

    return () => {
      clearInterval(interval);
      clearInterval(metricsInterval);
    };
  }, []);

  const loadMetrics = async () => {
    try {
      const data = await monitoringService.getMetrics();
      setMetrics(data);
    } catch (error) {
      console.error('Erro ao carregar métricas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRealTimeStats = async () => {
    try {
      const stats = await monitoringService.getRealTimeStats();
      setRealTimeStats(stats);
    } catch (error) {
      console.error('Erro ao carregar stats em tempo real:', error);
    }
  };

  const loadCacheStats = async () => {
    try {
      const stats = cacheService.getStats();
      setCacheStats(stats);
    } catch (error) {
      console.error('Erro ao carregar stats do cache:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'status-success';
      case 'warning': return 'status-warning';
      case 'error': return 'status-danger';
      default: return 'status-indicator';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center py-16">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>
          <p className="text-gray-600 text-lg">Carregando métricas...</p>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center py-16 max-w-2xl">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-3xl font-bold text-gray-800 mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Erro ao carregar métricas
          </h3>
          <p className="text-gray-600 text-lg">
            Não foi possível carregar as métricas do sistema. Tente novamente em alguns instantes.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-6">
        <div className="max-w-5xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Dashboard de Monitoramento
              </h2>
              <p className="text-gray-600 text-lg mt-2">
                Acompanhe o status e métricas do sistema em tempo real
              </p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
              <span className="text-blue-800 font-medium text-sm">
                Última atualização: {lastUpdate.toLocaleTimeString('pt-BR')}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-5xl mx-auto px-4 py-6 space-y-8">
        {/* Status Detalhado do Sistema */}
        <div>
          <h3 className="text-2xl font-bold text-gray-800 mb-6">Status Detalhado do Sistema</h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ConfigurationStatus />
            <WhatsAppStatus />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <SecurityStatus />
            <AntiBanMonitor />
          </div>
        </div>

        {/* Status em tempo real */}
        {realTimeStats && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h3 className="text-xl font-semibold text-gray-800">Status do Sistema</h3>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className={`inline-flex items-center space-x-2 px-3 py-2 rounded-lg border ${
                    realTimeStats.systemStatus === 'healthy'
                      ? 'bg-green-50 border-green-200'
                      : realTimeStats.systemStatus === 'warning'
                      ? 'bg-yellow-50 border-yellow-200'
                      : 'bg-red-50 border-red-200'
                  }`}>
                    {getStatusIcon(realTimeStats.systemStatus)}
                    <span className={`font-medium text-sm capitalize ${
                      realTimeStats.systemStatus === 'healthy'
                        ? 'text-green-800'
                        : realTimeStats.systemStatus === 'warning'
                        ? 'text-yellow-800'
                        : 'text-red-800'
                    }`}>{realTimeStats.systemStatus}</span>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-800">{realTimeStats.activeUsers}</div>
                  <div className="text-base text-gray-600">Usuários ativos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-800">{realTimeStats.messagesLastHour}</div>
                  <div className="text-base text-gray-600">Mensagens/hora</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-800">{metrics.avg_response_time}ms</div>
                  <div className="text-base text-gray-600">Tempo médio</div>
                </div>
              </div>
              {realTimeStats.lastError && (
                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium text-red-800">Último erro:</span>
                    <span className="text-red-700">{realTimeStats.lastError}</span>
                  </div>
                </div>
            )}
          </div>
        </div>
      )}

      {/* Métricas principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="card-modern">
          <div className="card-content">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gh-link rounded flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gh-text-secondary">Interações Totais</p>
                <p className="text-xl font-semibold text-gh-text">{metrics.total_interactions}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card-modern">
          <div className="card-content">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-success rounded flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gh-text-secondary">Documentos</p>
                <p className="text-xl font-semibold text-gh-text">{metrics.total_documents}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card-modern">
          <div className="card-content">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-warning rounded flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gh-text-secondary">Conversas</p>
                <p className="text-xl font-semibold text-gh-text">{metrics.total_conversations}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card-modern">
          <div className="card-content">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-info rounded flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gh-text-secondary">Taxa de Sucesso</p>
                <p className="text-xl font-semibold text-gh-text">{Math.round(metrics.success_rate * 100)}%</p>
              </div>
            </div>
          </div>
        </div>
      </div>

        {/* Estatísticas do Cache */}
        {cacheStats && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold text-gray-800">Performance do Cache</h3>
                <div className="flex items-center space-x-2">
                  <div className={`px-3 py-1 rounded-lg text-sm font-medium ${
                    cacheStats.hitRate > 70 ? 'bg-green-50 text-green-800 border border-green-200' :
                    cacheStats.hitRate > 50 ? 'bg-yellow-50 text-yellow-800 border border-yellow-200' :
                    'bg-red-50 text-red-800 border border-red-200'
                  }`}>
                    {cacheStats.hitRate}% Hit Rate
                  </div>
                </div>
              </div>
            </div>
            <div className="p-6">

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-2xl font-bold text-blue-600">{cacheStats.hits}</div>
                  <div className="text-base text-blue-700">Cache Hits</div>
                </div>

                <div className="text-center p-4 bg-red-50 rounded-lg border border-red-200">
                  <div className="text-2xl font-bold text-red-600">{cacheStats.misses}</div>
                  <div className="text-base text-red-700">Cache Misses</div>
                </div>

                <div className="text-center p-4 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="text-2xl font-bold text-purple-600">{cacheStats.cacheSize}</div>
                  <div className="text-base text-purple-700">Entradas</div>
                </div>

                <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-2xl font-bold text-green-600">
                    {(cacheStats.memoryUsage / 1024).toFixed(1)}KB
                  </div>
                  <div className="text-base text-green-700">Memória</div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between text-base">
                  <span className="text-gray-600">Total de Requisições:</span>
                  <span className="font-medium text-gray-900">{cacheStats.totalRequests}</span>
                </div>
                <div className="mt-3 w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${cacheStats.hitRate}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Queries populares */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Perguntas Mais Frequentes</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {metrics.popular_queries.map((query, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-800 font-medium">{query.query}</span>
                  <span className="text-base text-gray-600 bg-blue-50 border border-blue-200 px-3 py-1 rounded-lg">
                    {query.count} vezes
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Estatísticas diárias */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
            <h3 className="text-xl font-semibold text-gray-800">Atividade dos Últimos 7 Dias</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {metrics.daily_stats.map((day, index) => (
                <div key={index} className="grid grid-cols-4 gap-4 py-3 border-b border-gray-100 last:border-b-0">
                  <div className="font-medium text-gray-900">
                    {new Date(day.date).toLocaleDateString('pt-BR', {
                      weekday: 'short',
                      day: '2-digit',
                      month: '2-digit'
                    })}
                  </div>
                  <div className="text-center text-gray-600">{day.interactions} interações</div>
                  <div className="text-center text-gray-600">{day.documents_uploaded} docs</div>
                  <div className="text-center text-gray-600">{day.conversations_started} conversas</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
