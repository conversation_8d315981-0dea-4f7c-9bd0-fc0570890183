import express from 'express';
import cors from 'cors';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Estado simulado do WhatsApp
let whatsappState = {
  isConnected: false,
  sessionName: 'vereadora-rafaela',
  status: 'disconnected',
  connectionAttempts: 0,
  timestamp: new Date().toISOString(),
  qrCode: null
};

// Função para gerar QR code simulado
function generateSimulatedQRCode() {
  const timestamp = new Date().toISOString();
  const attempt = whatsappState.connectionAttempts + 1;

  // QR Code base64 simulado (um QR code real pequeno)
  const simulatedQRBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

  whatsappState.qrCode = {
    base64: simulatedQRBase64,
    ascii: '█▀▀▀▀▀█ ▀▀█▀▀ █▀▀▀▀▀█\n█ ███ █ ▀█▀█▀ █ ███ █\n█ ▀▀▀ █ █▀▀▀█ █ ▀▀▀ █\n▀▀▀▀▀▀▀ ▀ ▀ ▀ ▀▀▀▀▀▀▀\n▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀\n█▀▀▀▀▀█ ▀▀▀▀▀ █▀▀▀▀▀█\n█ ███ █ ▀▀▀▀▀ █ ███ █\n█ ▀▀▀ █ ▀▀▀▀▀ █ ▀▀▀ █\n▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀',
    path: `/tmp/qr-${Date.now()}.png`,
    timestamp: timestamp,
    attempt: attempt
  };

  whatsappState.connectionAttempts = attempt;
  whatsappState.status = 'waiting_for_qr_scan';
  whatsappState.timestamp = timestamp;

  console.log(`📱 QR Code gerado - Tentativa ${attempt}`);

  // Simular expiração do QR code após 20 segundos
  setTimeout(() => {
    if (whatsappState.qrCode && whatsappState.qrCode.attempt === attempt) {
      generateSimulatedQRCode(); // Gerar novo QR code
    }
  }, 20000);
}

// Middlewares
app.use(cors());
app.use(express.json());

// Basic routes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'Vereadora Rafaela Backend',
    port: PORT
  });
});

// WhatsApp Status Route
app.get('/api/whatsapp/status', (req, res) => {
  console.log('📱 Status solicitado:', whatsappState);
  res.json({
    success: true,
    data: {
      ...whatsappState,
      serviceType: 'simulated',
      isSimulator: true
    },
    message: 'Status obtido com sucesso'
  });
});

// WhatsApp QR Code Route
app.get('/api/whatsapp/qr', (req, res) => {
  console.log('📱 QR Code solicitado');

  if (!whatsappState.qrCode) {
    generateSimulatedQRCode();
  }

  res.json({
    success: true,
    data: {
      qrCode: whatsappState.qrCode
    },
    message: 'QR Code obtido com sucesso'
  });
});

// WhatsApp Restart Route
app.post('/api/whatsapp/restart', (req, res) => {
  console.log('📱 Reiniciando conexão WhatsApp...');

  whatsappState.isConnected = false;
  whatsappState.status = 'restarting';
  whatsappState.connectionAttempts = 0;
  whatsappState.qrCode = null;
  whatsappState.timestamp = new Date().toISOString();

  // Simular processo de reinicialização
  setTimeout(() => {
    generateSimulatedQRCode();
  }, 2000);

  res.json({
    success: true,
    message: 'Reinicialização iniciada'
  });
});

// Simular conexão após 30 segundos (para demonstração)
app.post('/api/whatsapp/simulate-connect', (req, res) => {
  console.log('📱 Simulando conexão WhatsApp...');

  whatsappState.isConnected = true;
  whatsappState.status = 'connected';
  whatsappState.qrCode = null;
  whatsappState.timestamp = new Date().toISOString();

  res.json({
    success: true,
    message: 'Conexão simulada com sucesso'
  });
});

// Inicializar com QR code
setTimeout(() => {
  generateSimulatedQRCode();
}, 1000);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Backend rodando em http://localhost:${PORT}`);
  console.log(`📱 WhatsApp API: http://localhost:${PORT}/api/whatsapp/status`);
  console.log(`🔗 QR Code API: http://localhost:${PORT}/api/whatsapp/qr`);
  console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🔄 Restart API: http://localhost:${PORT}/api/whatsapp/restart`);
  console.log(`🎭 Simulate Connect: http://localhost:${PORT}/api/whatsapp/simulate-connect`);
});
