import express from 'express';
import cors from 'cors';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';
import wppconnect from '@wppconnect-team/wppconnect';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Estado do WhatsApp
let whatsappState = {
  isConnected: false,
  sessionName: 'vereadora-rafaela',
  status: 'initializing',
  connectionAttempts: 0,
  timestamp: new Date().toISOString(),
  qrCode: null,
  client: null
};

// Função para inicializar WPPConnect
async function initializeWPPConnect() {
  try {
    console.log('🚀 Inicializando WPPConnect...');

    whatsappState.status = 'initializing';
    whatsappState.timestamp = new Date().toISOString();

    const client = await wppconnect.create({
      session: whatsappState.sessionName,
      catchQR: (base64Qr, asciiQR, attempts) => {
        console.log('📱 QR Code recebido - Tentativa:', attempts);

        // Processar o QR code base64
        let qrCodeBase64 = base64Qr;
        if (base64Qr.startsWith('data:image/png;base64,')) {
          qrCodeBase64 = base64Qr.replace('data:image/png;base64,', '');
        }

        // Salvar QR code como arquivo
        const qrPath = join(__dirname, 'data', `qr-${Date.now()}.png`);
        try {
          const matches = base64Qr.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);
          if (matches && matches.length === 3) {
            const imageBuffer = Buffer.from(matches[2], 'base64');

            // Criar diretório se não existir
            const dataDir = join(__dirname, 'data');
            if (!fs.existsSync(dataDir)) {
              fs.mkdirSync(dataDir, { recursive: true });
            }

            fs.writeFileSync(qrPath, imageBuffer);
            console.log('💾 QR Code salvo em:', qrPath);
          }
        } catch (error) {
          console.error('❌ Erro ao salvar QR Code:', error);
        }

        // Atualizar estado
        whatsappState.qrCode = {
          base64: qrCodeBase64,
          ascii: asciiQR,
          path: qrPath,
          timestamp: new Date().toISOString(),
          attempt: attempts || whatsappState.connectionAttempts + 1
        };

        whatsappState.connectionAttempts = attempts || whatsappState.connectionAttempts + 1;
        whatsappState.status = 'waiting_for_qr_scan';
        whatsappState.timestamp = new Date().toISOString();

        console.log('✅ QR Code atualizado no estado');
      },
      statusFind: (statusSession, session) => {
        console.log('📊 Status da sessão:', statusSession, 'Sessão:', session);

        switch (statusSession) {
          case 'notLogged':
            whatsappState.status = 'disconnected';
            whatsappState.isConnected = false;
            break;
          case 'qrReadSuccess':
            whatsappState.status = 'qr_read_success';
            whatsappState.qrCode = null;
            break;
          case 'chatsAvailable':
            whatsappState.status = 'connected';
            whatsappState.isConnected = true;
            whatsappState.qrCode = null;
            console.log('🎉 WhatsApp conectado com sucesso!');
            break;
          case 'deviceNotConnected':
            whatsappState.status = 'device_not_connected';
            whatsappState.isConnected = false;
            break;
          default:
            whatsappState.status = statusSession;
        }

        whatsappState.timestamp = new Date().toISOString();
      },
      logQR: false,
      headless: true,
      devtools: false,
      useChrome: true,
      debug: false,
      browserArgs: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu'
      ]
    });

    whatsappState.client = client;
    console.log('✅ WPPConnect inicializado com sucesso');

    return client;

  } catch (error) {
    console.error('❌ Erro ao inicializar WPPConnect:', error);
    whatsappState.status = 'error';
    whatsappState.timestamp = new Date().toISOString();
    throw error;
  }
}

// Middlewares
app.use(cors());
app.use(express.json());

// Servir arquivos estáticos (QR codes)
app.use('/qr', express.static(join(__dirname, 'data')));

// Basic routes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'Vereadora Rafaela Backend',
    port: PORT,
    whatsapp: {
      status: whatsappState.status,
      connected: whatsappState.isConnected,
      hasQR: !!whatsappState.qrCode
    }
  });
});

// WhatsApp Status Route
app.get('/api/whatsapp/status', (req, res) => {
  console.log('📱 Status solicitado:', {
    status: whatsappState.status,
    connected: whatsappState.isConnected,
    hasQR: !!whatsappState.qrCode,
    attempts: whatsappState.connectionAttempts
  });

  res.json({
    success: true,
    data: {
      ...whatsappState,
      serviceType: 'wppconnect',
      isSimulator: false,
      isReal: true
    },
    message: 'Status obtido com sucesso'
  });
});

// WhatsApp QR Code Route
app.get('/api/whatsapp/qr', (req, res) => {
  console.log('📱 QR Code solicitado');

  if (!whatsappState.qrCode) {
    return res.json({
      success: false,
      data: {
        qrCode: null
      },
      message: 'QR Code não disponível. Aguarde a inicialização.'
    });
  }

  res.json({
    success: true,
    data: {
      qrCode: whatsappState.qrCode
    },
    message: 'QR Code obtido com sucesso'
  });
});

// WhatsApp Restart Route
app.post('/api/whatsapp/restart', async (req, res) => {
  console.log('📱 Reiniciando conexão WhatsApp...');

  try {
    // Fechar cliente atual se existir
    if (whatsappState.client) {
      try {
        await whatsappState.client.close();
      } catch (error) {
        console.warn('⚠️ Erro ao fechar cliente:', error);
      }
    }

    // Resetar estado
    whatsappState.isConnected = false;
    whatsappState.status = 'restarting';
    whatsappState.connectionAttempts = 0;
    whatsappState.qrCode = null;
    whatsappState.client = null;
    whatsappState.timestamp = new Date().toISOString();

    // Reinicializar WPPConnect
    setTimeout(async () => {
      try {
        await initializeWPPConnect();
      } catch (error) {
        console.error('❌ Erro ao reinicializar:', error);
      }
    }, 2000);

    res.json({
      success: true,
      message: 'Reinicialização iniciada'
    });

  } catch (error) {
    console.error('❌ Erro ao reiniciar:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao reiniciar conexão'
    });
  }
});

// Enviar mensagem (para testes)
app.post('/api/whatsapp/send', async (req, res) => {
  const { to, message } = req.body;

  if (!whatsappState.client || !whatsappState.isConnected) {
    return res.status(400).json({
      success: false,
      message: 'WhatsApp não está conectado'
    });
  }

  try {
    const result = await whatsappState.client.sendText(to, message);
    res.json({
      success: true,
      data: result,
      message: 'Mensagem enviada com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao enviar mensagem:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao enviar mensagem'
    });
  }
});

// Inicializar WPPConnect
setTimeout(async () => {
  try {
    await initializeWPPConnect();
  } catch (error) {
    console.error('❌ Erro na inicialização:', error);
  }
}, 2000);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Backend rodando em http://localhost:${PORT}`);
  console.log(`📱 WhatsApp API: http://localhost:${PORT}/api/whatsapp/status`);
  console.log(`🔗 QR Code API: http://localhost:${PORT}/api/whatsapp/qr`);
  console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🔄 Restart API: http://localhost:${PORT}/api/whatsapp/restart`);
  console.log(`📤 Send Message: http://localhost:${PORT}/api/whatsapp/send`);
  console.log(`📁 QR Files: http://localhost:${PORT}/qr/`);
  console.log('');
  console.log('🔄 Inicializando WPPConnect em 2 segundos...');
});
