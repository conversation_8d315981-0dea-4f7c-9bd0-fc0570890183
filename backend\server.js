import express from 'express';
import cors from 'cors';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Estado do WhatsApp
let whatsappState = {
  isConnected: false,
  sessionName: 'vereadora-rafaela',
  status: 'initializing',
  connectionAttempts: 0,
  timestamp: new Date().toISOString(),
  qrCode: null,
  client: null,
  syncStatus: {
    isSync: false,
    totalChats: 0,
    syncedChats: 0,
    currentChat: null,
    startTime: null,
    endTime: null
  }
};

// Função para sincronizar todas as conversas e mensagens
async function syncAllChatsAndMessages(client) {
  if (!client) {
    console.error('❌ Cliente não disponível para sincronização');
    return;
  }

  try {
    console.log('🔄 Iniciando sincronização automática de conversas...');

    whatsappState.syncStatus.isSync = true;
    whatsappState.syncStatus.startTime = new Date().toISOString();
    whatsappState.syncStatus.syncedChats = 0;

    // Obter todas as conversas
    const chats = await client.getAllChats();
    console.log(`📱 Encontradas ${chats.length} conversas para sincronizar`);

    whatsappState.syncStatus.totalChats = chats.length;

    // Sincronizar cada conversa (limitando a 20 conversas mais recentes para não sobrecarregar)
    const chatsToSync = chats.slice(0, 20);

    for (let i = 0; i < chatsToSync.length; i++) {
      const chat = chatsToSync[i];

      try {
        whatsappState.syncStatus.currentChat = chat.name || chat.id;
        console.log(`📥 Sincronizando conversa ${i + 1}/${chatsToSync.length}: ${chat.name || chat.id}`);

        // Carregar histórico completo da conversa usando a função WAPI
        await client.sendExposedFunction('loadAllEarlierMessages', chat.id);

        whatsappState.syncStatus.syncedChats++;
        console.log(`✅ Conversa sincronizada: ${chat.name || chat.id}`);

        // Pequena pausa entre sincronizações para não sobrecarregar
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.error(`❌ Erro ao sincronizar conversa ${chat.name || chat.id}:`, error);
      }
    }

    whatsappState.syncStatus.isSync = false;
    whatsappState.syncStatus.endTime = new Date().toISOString();
    whatsappState.syncStatus.currentChat = null;

    console.log(`🎉 Sincronização concluída! ${whatsappState.syncStatus.syncedChats}/${whatsappState.syncStatus.totalChats} conversas sincronizadas`);

  } catch (error) {
    console.error('❌ Erro durante sincronização:', error);
    whatsappState.syncStatus.isSync = false;
    whatsappState.syncStatus.endTime = new Date().toISOString();
  }
}

// Função para obter mensagens de uma conversa específica
async function getChatMessages(client, chatId, limit = 50) {
  try {
    const messages = await client.getMessages(chatId, limit);
    return messages;
  } catch (error) {
    console.error(`❌ Erro ao obter mensagens da conversa ${chatId}:`, error);
    return [];
  }
}

// Função para simular QR code (temporário até WPPConnect funcionar)
function generateSimulatedQRCode() {
  const timestamp = new Date().toISOString();
  const attempt = whatsappState.connectionAttempts + 1;

  // QR Code base64 simulado mais realista
  const simulatedQRBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

  whatsappState.qrCode = {
    base64: simulatedQRBase64,
    ascii: '█▀▀▀▀▀█ ▀▀█▀▀ █▀▀▀▀▀█\n█ ███ █ ▀█▀█▀ █ ███ █\n█ ▀▀▀ █ █▀▀▀█ █ ▀▀▀ █\n▀▀▀▀▀▀▀ ▀ ▀ ▀ ▀▀▀▀▀▀▀',
    path: `/tmp/qr-${Date.now()}.png`,
    timestamp: timestamp,
    attempt: attempt
  };

  whatsappState.connectionAttempts = attempt;
  whatsappState.status = 'waiting_for_qr_scan';
  whatsappState.timestamp = timestamp;

  console.log(`📱 QR Code simulado gerado - Tentativa ${attempt}`);

  // Simular expiração do QR code após 30 segundos
  setTimeout(() => {
    if (whatsappState.qrCode && whatsappState.qrCode.attempt === attempt) {
      generateSimulatedQRCode(); // Gerar novo QR code
    }
  }, 30000);
}

// Função para inicializar WPPConnect (comentada temporariamente)
async function initializeWPPConnect() {
  try {
    console.log('🚀 Inicializando modo simulado (WPPConnect será ativado em breve)...');

    whatsappState.status = 'initializing';
    whatsappState.timestamp = new Date().toISOString();

    // Simular inicialização
    setTimeout(() => {
      generateSimulatedQRCode();
    }, 2000);

    return null;

    /* TODO: Ativar WPPConnect quando resolver problemas de importação
    const client = await wppconnect.create({
      session: whatsappState.sessionName,
      catchQR: (base64Qr, asciiQR, attempts) => {
        // Código do QR será implementado aqui
      },
      // Configurações do WPPConnect serão implementadas aqui
    });
    */

  } catch (error) {
    console.error('❌ Erro ao inicializar WPPConnect:', error);
    whatsappState.status = 'error';
    whatsappState.timestamp = new Date().toISOString();
    throw error;
  }
}

// Middlewares
app.use(cors());
app.use(express.json());

// Servir arquivos estáticos (QR codes)
app.use('/qr', express.static(join(__dirname, 'data')));

// Basic routes
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'Vereadora Rafaela Backend',
    port: PORT,
    whatsapp: {
      status: whatsappState.status,
      connected: whatsappState.isConnected,
      hasQR: !!whatsappState.qrCode
    }
  });
});

// WhatsApp Status Route
app.get('/api/whatsapp/status', (req, res) => {
  console.log('📱 Status solicitado:', {
    status: whatsappState.status,
    connected: whatsappState.isConnected,
    hasQR: !!whatsappState.qrCode,
    attempts: whatsappState.connectionAttempts,
    syncStatus: whatsappState.syncStatus
  });

  res.json({
    success: true,
    data: {
      ...whatsappState,
      serviceType: 'wppconnect',
      isSimulator: false,
      isReal: true,
      syncStatus: whatsappState.syncStatus
    },
    message: 'Status obtido com sucesso'
  });
});

// WhatsApp QR Code Route
app.get('/api/whatsapp/qr', (req, res) => {
  console.log('📱 QR Code solicitado');

  if (!whatsappState.qrCode) {
    return res.json({
      success: false,
      data: {
        qrCode: null
      },
      message: 'QR Code não disponível. Aguarde a inicialização.'
    });
  }

  res.json({
    success: true,
    data: {
      qrCode: whatsappState.qrCode
    },
    message: 'QR Code obtido com sucesso'
  });
});

// WhatsApp Restart Route
app.post('/api/whatsapp/restart', async (req, res) => {
  console.log('📱 Reiniciando conexão WhatsApp...');

  try {
    // Fechar cliente atual se existir
    if (whatsappState.client) {
      try {
        await whatsappState.client.close();
      } catch (error) {
        console.warn('⚠️ Erro ao fechar cliente:', error);
      }
    }

    // Resetar estado
    whatsappState.isConnected = false;
    whatsappState.status = 'restarting';
    whatsappState.connectionAttempts = 0;
    whatsappState.qrCode = null;
    whatsappState.client = null;
    whatsappState.timestamp = new Date().toISOString();

    // Reinicializar WPPConnect
    setTimeout(async () => {
      try {
        await initializeWPPConnect();
      } catch (error) {
        console.error('❌ Erro ao reinicializar:', error);
      }
    }, 2000);

    res.json({
      success: true,
      message: 'Reinicialização iniciada'
    });

  } catch (error) {
    console.error('❌ Erro ao reiniciar:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao reiniciar conexão'
    });
  }
});

// Obter todas as conversas
app.get('/api/whatsapp/chats', async (req, res) => {
  if (!whatsappState.client || !whatsappState.isConnected) {
    return res.status(400).json({
      success: false,
      message: 'WhatsApp não está conectado'
    });
  }

  try {
    const chats = await whatsappState.client.getAllChats();
    res.json({
      success: true,
      data: chats,
      message: 'Conversas obtidas com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao obter conversas:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao obter conversas'
    });
  }
});

// Obter mensagens de uma conversa específica
app.get('/api/whatsapp/chats/:chatId/messages', async (req, res) => {
  const { chatId } = req.params;
  const { limit = 50 } = req.query;

  if (!whatsappState.client || !whatsappState.isConnected) {
    return res.status(400).json({
      success: false,
      message: 'WhatsApp não está conectado'
    });
  }

  try {
    const messages = await getChatMessages(whatsappState.client, chatId, parseInt(limit));
    res.json({
      success: true,
      data: messages,
      message: 'Mensagens obtidas com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao obter mensagens:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao obter mensagens'
    });
  }
});

// Sincronizar histórico de uma conversa específica
app.post('/api/whatsapp/chats/:chatId/sync-history', async (req, res) => {
  const { chatId } = req.params;

  if (!whatsappState.client || !whatsappState.isConnected) {
    return res.status(400).json({
      success: false,
      message: 'WhatsApp não está conectado'
    });
  }

  try {
    console.log(`🔄 Sincronizando histórico da conversa: ${chatId}`);
    await whatsappState.client.sendExposedFunction('loadAllEarlierMessages', chatId);

    res.json({
      success: true,
      message: 'Histórico sincronizado com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao sincronizar histórico:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao sincronizar histórico'
    });
  }
});

// Iniciar sincronização manual de todas as conversas
app.post('/api/whatsapp/sync', async (req, res) => {
  if (!whatsappState.client || !whatsappState.isConnected) {
    return res.status(400).json({
      success: false,
      message: 'WhatsApp não está conectado'
    });
  }

  if (whatsappState.syncStatus.isSync) {
    return res.status(400).json({
      success: false,
      message: 'Sincronização já está em andamento'
    });
  }

  try {
    // Iniciar sincronização em background
    syncAllChatsAndMessages(whatsappState.client);

    res.json({
      success: true,
      message: 'Sincronização iniciada'
    });
  } catch (error) {
    console.error('❌ Erro ao iniciar sincronização:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao iniciar sincronização'
    });
  }
});

// Obter status da sincronização
app.get('/api/whatsapp/sync/status', (req, res) => {
  res.json({
    success: true,
    data: whatsappState.syncStatus,
    message: 'Status da sincronização obtido com sucesso'
  });
});

// Enviar mensagem (para testes)
app.post('/api/whatsapp/send', async (req, res) => {
  const { to, message } = req.body;

  if (!whatsappState.client || !whatsappState.isConnected) {
    return res.status(400).json({
      success: false,
      message: 'WhatsApp não está conectado'
    });
  }

  try {
    const result = await whatsappState.client.sendText(to, message);
    res.json({
      success: true,
      data: result,
      message: 'Mensagem enviada com sucesso'
    });
  } catch (error) {
    console.error('❌ Erro ao enviar mensagem:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao enviar mensagem'
    });
  }
});

// Inicializar WPPConnect
setTimeout(async () => {
  try {
    await initializeWPPConnect();
  } catch (error) {
    console.error('❌ Erro na inicialização:', error);
  }
}, 2000);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Backend rodando em http://localhost:${PORT}`);
  console.log(`📱 WhatsApp API: http://localhost:${PORT}/api/whatsapp/status`);
  console.log(`🔗 QR Code API: http://localhost:${PORT}/api/whatsapp/qr`);
  console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🔄 Restart API: http://localhost:${PORT}/api/whatsapp/restart`);
  console.log(`📤 Send Message: http://localhost:${PORT}/api/whatsapp/send`);
  console.log(`💬 Chats API: http://localhost:${PORT}/api/whatsapp/chats`);
  console.log(`🔄 Sync API: http://localhost:${PORT}/api/whatsapp/sync`);
  console.log(`📊 Sync Status: http://localhost:${PORT}/api/whatsapp/sync/status`);
  console.log(`📁 QR Files: http://localhost:${PORT}/qr/`);
  console.log('');
  console.log('🔄 Inicializando WPPConnect em 2 segundos...');
  console.log('📱 Sincronização automática será ativada após conexão via QR');
});
