#!/usr/bin/env node

/**
 * Script de inicialização robusta do servidor
 * Mantém o servidor rodando e reinicia automaticamente em caso de falha
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Iniciando servidor robusto...');

// Configurações
const PORT = process.env.PORT || 3003;
const MAX_RESTARTS = 5;
const RESTART_DELAY = 2000; // 2 segundos

let restartCount = 0;
let serverProcess = null;

function startServer() {
  console.log(`\n🔄 Tentativa ${restartCount + 1}/${MAX_RESTARTS}`);
  console.log(`📍 Porta: ${PORT}`);
  console.log(`⏰ ${new Date().toLocaleString('pt-BR')}`);
  
  // Definir variáveis de ambiente
  const env = {
    ...process.env,
    PORT: PORT,
    NODE_ENV: process.env.NODE_ENV || 'development'
  };

  // Iniciar processo do servidor
  serverProcess = spawn('node', ['server-complete.js'], {
    cwd: __dirname,
    env: env,
    stdio: 'inherit'
  });

  // Listener para quando o processo termina
  serverProcess.on('close', (code) => {
    console.log(`\n⚠️ Servidor parou com código: ${code}`);
    
    if (code !== 0 && restartCount < MAX_RESTARTS) {
      restartCount++;
      console.log(`🔄 Reiniciando em ${RESTART_DELAY/1000} segundos...`);
      
      setTimeout(() => {
        startServer();
      }, RESTART_DELAY);
    } else if (restartCount >= MAX_RESTARTS) {
      console.log('❌ Máximo de reinicializações atingido. Parando...');
      process.exit(1);
    } else {
      console.log('✅ Servidor parou normalmente.');
      process.exit(0);
    }
  });

  // Listener para erros
  serverProcess.on('error', (err) => {
    console.error('❌ Erro ao iniciar servidor:', err);
    
    if (restartCount < MAX_RESTARTS) {
      restartCount++;
      console.log(`🔄 Tentando novamente em ${RESTART_DELAY/1000} segundos...`);
      
      setTimeout(() => {
        startServer();
      }, RESTART_DELAY);
    } else {
      console.log('❌ Máximo de tentativas atingido. Parando...');
      process.exit(1);
    }
  });

  // Reset contador de restart quando servidor roda por mais de 30 segundos
  setTimeout(() => {
    if (serverProcess && !serverProcess.killed) {
      console.log('✅ Servidor estável - resetando contador de restarts');
      restartCount = 0;
    }
  }, 30000);
}

// Handlers para sinais do sistema
process.on('SIGINT', () => {
  console.log('\n🛑 Recebido SIGINT - parando servidor...');
  if (serverProcess) {
    serverProcess.kill('SIGINT');
  }
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Recebido SIGTERM - parando servidor...');
  if (serverProcess) {
    serverProcess.kill('SIGTERM');
  }
  process.exit(0);
});

// Iniciar servidor
console.log('🎉 ==========================================');
console.log('🚀  INICIADOR ROBUSTO DO SERVIDOR');
console.log('🎉 ==========================================');
console.log(`📍 Porta: ${PORT}`);
console.log(`🔄 Max restarts: ${MAX_RESTARTS}`);
console.log(`⏱️ Delay restart: ${RESTART_DELAY}ms`);
console.log('🎉 ==========================================\n');

startServer();
