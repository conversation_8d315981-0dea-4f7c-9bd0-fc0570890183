const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3002;

// Estado do WhatsApp
let whatsappState = {
  isConnected: false,
  sessionName: 'vereadora-rafaela',
  status: 'initializing',
  connectionAttempts: 0,
  timestamp: new Date().toISOString(),
  qrCode: null,
  client: null,
  syncStatus: {
    isSync: false,
    totalChats: 0,
    syncedChats: 0,
    currentChat: null,
    startTime: null,
    endTime: null
  }
};

// Função para gerar QR code simulado
function generateSimulatedQRCode() {
  const timestamp = new Date().toISOString();
  const attempt = whatsappState.connectionAttempts + 1;
  
  // QR Code base64 simulado mais realista
  const simulatedQRBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
  
  whatsappState.qrCode = {
    base64: simulatedQRBase64,
    ascii: '█▀▀▀▀▀█ ▀▀█▀▀ █▀▀▀▀▀█\n█ ███ █ ▀█▀█▀ █ ███ █\n█ ▀▀▀ █ █▀▀▀█ █ ▀▀▀ █\n▀▀▀▀▀▀▀ ▀ ▀ ▀ ▀▀▀▀▀▀▀',
    path: `/tmp/qr-${Date.now()}.png`,
    timestamp: timestamp,
    attempt: attempt
  };
  
  whatsappState.connectionAttempts = attempt;
  whatsappState.status = 'waiting_for_qr_scan';
  whatsappState.timestamp = timestamp;
  
  console.log(`📱 QR Code simulado gerado - Tentativa ${attempt}`);
  
  // Simular expiração do QR code após 30 segundos
  setTimeout(() => {
    if (whatsappState.qrCode && whatsappState.qrCode.attempt === attempt) {
      generateSimulatedQRCode(); // Gerar novo QR code
    }
  }, 30000);
}

// Simular conexão após escanear QR
function simulateConnection() {
  setTimeout(() => {
    whatsappState.isConnected = true;
    whatsappState.status = 'connected';
    whatsappState.qrCode = null;
    whatsappState.timestamp = new Date().toISOString();
    
    console.log('🎉 Conexão simulada estabelecida!');
    
    // Simular sincronização
    setTimeout(() => {
      simulateSync();
    }, 2000);
  }, 45000); // Simular conexão após 45 segundos
}

// Simular sincronização de conversas
function simulateSync() {
  whatsappState.syncStatus.isSync = true;
  whatsappState.syncStatus.startTime = new Date().toISOString();
  whatsappState.syncStatus.totalChats = 10;
  whatsappState.syncStatus.syncedChats = 0;
  
  console.log('🔄 Iniciando sincronização simulada...');
  
  const chats = [
    'João Silva', 'Maria Santos', 'Pedro Oliveira', 'Ana Costa',
    'Carlos Lima', 'Lucia Ferreira', 'Roberto Alves', 'Fernanda Rocha',
    'Marcos Pereira', 'Juliana Souza'
  ];
  
  let currentIndex = 0;
  const syncInterval = setInterval(() => {
    if (currentIndex < chats.length) {
      whatsappState.syncStatus.currentChat = chats[currentIndex];
      whatsappState.syncStatus.syncedChats = currentIndex + 1;
      
      console.log(`📥 Sincronizando: ${chats[currentIndex]} (${currentIndex + 1}/${chats.length})`);
      currentIndex++;
    } else {
      clearInterval(syncInterval);
      whatsappState.syncStatus.isSync = false;
      whatsappState.syncStatus.endTime = new Date().toISOString();
      whatsappState.syncStatus.currentChat = null;
      
      console.log('✅ Sincronização simulada concluída!');
    }
  }, 2000);
}

// Middlewares
app.use(cors());
app.use(express.json());

// Servir arquivos estáticos (QR codes)
app.use('/qr', express.static(path.join(__dirname, 'data')));

// Basic routes
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'Vereadora Rafaela Backend',
    port: PORT,
    whatsapp: {
      status: whatsappState.status,
      connected: whatsappState.isConnected,
      hasQR: !!whatsappState.qrCode
    }
  });
});

// WhatsApp Status Route
app.get('/api/whatsapp/status', (req, res) => {
  console.log('📱 Status solicitado:', {
    status: whatsappState.status,
    connected: whatsappState.isConnected,
    hasQR: !!whatsappState.qrCode,
    attempts: whatsappState.connectionAttempts,
    syncStatus: whatsappState.syncStatus
  });
  
  res.json({ 
    success: true,
    data: {
      ...whatsappState,
      serviceType: 'simulated',
      isSimulator: true,
      isReal: false,
      syncStatus: whatsappState.syncStatus
    },
    message: 'Status obtido com sucesso'
  });
});

// WhatsApp QR Code Route
app.get('/api/whatsapp/qr', (req, res) => {
  console.log('📱 QR Code solicitado');
  
  if (!whatsappState.qrCode) {
    return res.json({ 
      success: false,
      data: {
        qrCode: null
      },
      message: 'QR Code não disponível. Aguarde a inicialização.'
    });
  }
  
  res.json({ 
    success: true,
    data: {
      qrCode: whatsappState.qrCode
    },
    message: 'QR Code obtido com sucesso'
  });
});

// WhatsApp Restart Route
app.post('/api/whatsapp/restart', async (req, res) => {
  console.log('📱 Reiniciando conexão WhatsApp...');
  
  // Resetar estado
  whatsappState.isConnected = false;
  whatsappState.status = 'restarting';
  whatsappState.connectionAttempts = 0;
  whatsappState.qrCode = null;
  whatsappState.client = null;
  whatsappState.timestamp = new Date().toISOString();
  
  // Reinicializar
  setTimeout(() => {
    generateSimulatedQRCode();
    simulateConnection();
  }, 2000);
  
  res.json({ 
    success: true,
    message: 'Reinicialização iniciada'
  });
});

// Simular conexão manual
app.post('/api/whatsapp/simulate-connect', (req, res) => {
  console.log('📱 Simulando conexão manual...');
  
  whatsappState.isConnected = true;
  whatsappState.status = 'connected';
  whatsappState.qrCode = null;
  whatsappState.timestamp = new Date().toISOString();
  
  // Iniciar sincronização
  setTimeout(() => {
    simulateSync();
  }, 1000);
  
  res.json({ 
    success: true,
    message: 'Conexão simulada com sucesso'
  });
});

// Obter status da sincronização
app.get('/api/whatsapp/sync/status', (req, res) => {
  res.json({
    success: true,
    data: whatsappState.syncStatus,
    message: 'Status da sincronização obtido com sucesso'
  });
});

// ===== ROTAS DE DOCUMENTOS =====

// Lista de documentos simulados (em produção viria do banco de dados)
let documentsDatabase = [
  {
    id: '1',
    name: 'Exemplo - Regimento Interno.pdf',
    size: 1024 * 1024 * 2.5, // 2.5MB
    type: 'application/pdf',
    status: 'ready', // Mudado de 'processed' para 'ready'
    upload_date: new Date(Date.now() - 86400000), // 1 dia atrás
    processed_at: new Date(Date.now() - 86400000 + 30000), // 30s depois
    chunks_count: 45
  },
  {
    id: '2',
    name: 'Exemplo - Lei Orgânica Municipal.pdf',
    size: 1024 * 1024 * 4.2, // 4.2MB
    type: 'application/pdf',
    status: 'ready', // Mudado de 'processed' para 'ready'
    upload_date: new Date(Date.now() - 172800000), // 2 dias atrás
    processed_at: new Date(Date.now() - 172800000 + 60000), // 1min depois
    chunks_count: 78
  }
];

// Obter todos os documentos
app.get('/api/documents', (req, res) => {
  console.log('📄 Documentos solicitados');
  res.json({
    success: true,
    documents: documentsDatabase,
    message: 'Documentos obtidos com sucesso'
  });
});

// Upload de documento (simulado - aceita FormData)
app.post('/api/documents/upload', (req, res) => {
  console.log('📤 Upload de documento recebido');
  console.log('📄 Headers:', req.headers['content-type']);

  // Simular processamento do arquivo enviado
  let fileName = 'Documento-Desconhecido.pdf';
  let fileSize = Math.floor(Math.random() * 5000000) + 1000000; // 1-5MB

  // Tentar extrair informações do FormData se disponível
  if (req.headers['content-type'] && req.headers['content-type'].includes('multipart/form-data')) {
    // Em uma implementação real, usaríamos multer aqui
    // Por enquanto, simular com dados aleatórios
    const timestamp = Date.now();
    fileName = `Documento-${timestamp}.pdf`;
  }

  // Simular criação de documento
  const newDocument = {
    id: Date.now().toString(),
    name: fileName,
    size: fileSize,
    type: 'application/pdf',
    status: 'processing',
    upload_date: new Date(), // Usar Date object em vez de string
    processed_at: null,
    chunks_count: 0
  };

  documentsDatabase.push(newDocument);
  console.log(`📄 Documento adicionado: ${newDocument.name} (ID: ${newDocument.id})`);

  // Simular processamento após 3 segundos
  setTimeout(() => {
    const docIndex = documentsDatabase.findIndex(d => d.id === newDocument.id);
    if (docIndex !== -1) {
      documentsDatabase[docIndex].status = 'ready'; // Mudado para 'ready'
      documentsDatabase[docIndex].processed_at = new Date(); // Usar Date object
      documentsDatabase[docIndex].chunks_count = Math.floor(Math.random() * 50) + 10;
      console.log(`✅ Documento ${newDocument.name} processado com sucesso`);
    }
  }, 3000);

  res.json({
    success: true,
    document: newDocument,
    message: 'Upload iniciado com sucesso'
  });
});

// Deletar documento
app.delete('/api/documents/:id', (req, res) => {
  const { id } = req.params;
  console.log(`🗑️ Deletando documento: ${id}`);

  const docIndex = documentsDatabase.findIndex(d => d.id === id);
  if (docIndex !== -1) {
    const deletedDoc = documentsDatabase.splice(docIndex, 1)[0];
    res.json({
      success: true,
      message: `Documento "${deletedDoc.name}" deletado com sucesso`
    });
  } else {
    res.status(404).json({
      success: false,
      message: 'Documento não encontrado'
    });
  }
});

// Inicializar com QR code
setTimeout(() => {
  generateSimulatedQRCode();
  simulateConnection();
}, 2000);

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Backend rodando em http://localhost:${PORT}`);
  console.log(`📱 WhatsApp API: http://localhost:${PORT}/api/whatsapp/status`);
  console.log(`🔗 QR Code API: http://localhost:${PORT}/api/whatsapp/qr`);
  console.log(`💚 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🔄 Restart API: http://localhost:${PORT}/api/whatsapp/restart`);
  console.log(`🎭 Simulate Connect: http://localhost:${PORT}/api/whatsapp/simulate-connect`);
  console.log(`📊 Sync Status: http://localhost:${PORT}/api/whatsapp/sync/status`);
  console.log(`📄 Documents API: http://localhost:${PORT}/api/documents`);
  console.log(`📤 Upload API: http://localhost:${PORT}/api/documents/upload`);
  console.log(`📁 QR Files: http://localhost:${PORT}/qr/`);
  console.log('');
  console.log('🔄 Inicializando modo simulado em 2 segundos...');
  console.log('📱 Sincronização automática será ativada após conexão via QR');
  console.log('📄 Base de documentos simulada com 2 documentos de exemplo');
  console.log('');
  console.log('💡 Para testar: use /api/whatsapp/simulate-connect para simular conexão manual');
  console.log('📱 QR Code será exibido no frontend e renovado automaticamente');
  console.log('🎯 Após 45 segundos, a conexão será simulada automaticamente');
});
