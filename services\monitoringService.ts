import { databaseService } from './databaseService';
import type { MonitoringMetrics, InteractionLog } from '../types';

class MonitoringService {
  async recordInteraction(
    type: InteractionLog['type'],
    metadata: Record<string, any>
  ): Promise<void> {
    try {
      // Tentar registrar via backend
      const response = await fetch('http://localhost:3003/api/analytics/log', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type,
          timestamp: new Date().toISOString(),
          metadata
        })
      });

      if (response.ok) {
        console.log('📊 Interação registrada via backend:', type);
      } else {
        console.warn('⚠️ Falha ao registrar via backend:', response.status);
      }
    } catch (error) {
      console.warn('⚠️ Erro ao registrar interação:', error);
      // Não propagar erro para não afetar funcionalidade principal
    }
  }

  async getMetrics(): Promise<MonitoringMetrics> {
    try {
      const logs = await databaseService.getInteractionLogs(1000);
      const documents = await databaseService.getDocuments();
      const conversations = await databaseService.getConversations();

      return this.calculateMetrics(logs, documents, conversations);
    } catch (error) {
      console.error('Erro ao obter métricas:', error);
      return this.getEmptyMetrics();
    }
  }

  private calculateMetrics(
    logs: InteractionLog[],
    documents: any[],
    conversations: any[]
  ): MonitoringMetrics {
    const now = new Date();
    const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Filtrar logs dos últimos 30 dias
    const recentLogs = logs.filter(log => log.timestamp >= last30Days);

    // Calcular métricas básicas
    const totalInteractions = recentLogs.length;
    const totalDocuments = documents.length;
    const totalConversations = conversations.length;

    // Calcular tempo médio de resposta
    const responseTimes = recentLogs
      .filter(log => log.type === 'message_received' && log.metadata.processing_time)
      .map(log => log.metadata.processing_time);
    
    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

    // Calcular taxa de sucesso
    const successfulInteractions = recentLogs.filter(log => 
      log.type !== 'error' && log.type !== 'document_upload_error'
    ).length;
    
    const successRate = totalInteractions > 0 
      ? successfulInteractions / totalInteractions 
      : 1;

    const errorRate = 1 - successRate;

    // Queries populares (simulado - em produção analisar conteúdo das mensagens)
    const popularQueries = [
      { query: 'Como funciona a Câmara Municipal?', count: 15 },
      { query: 'Projetos de lei em andamento', count: 12 },
      { query: 'Horário de funcionamento', count: 10 },
      { query: 'Como entrar em contato', count: 8 },
      { query: 'Serviços disponíveis', count: 6 }
    ];

    // Estatísticas diárias
    const dailyStats = this.calculateDailyStats(recentLogs, 7);

    return {
      total_interactions: totalInteractions,
      total_documents: totalDocuments,
      total_conversations: totalConversations,
      avg_response_time: Math.round(avgResponseTime),
      success_rate: Math.round(successRate * 100) / 100,
      error_rate: Math.round(errorRate * 100) / 100,
      popular_queries: popularQueries,
      daily_stats: dailyStats
    };
  }

  private calculateDailyStats(logs: InteractionLog[], days: number) {
    const stats = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayStart = new Date(date.setHours(0, 0, 0, 0));
      const dayEnd = new Date(date.setHours(23, 59, 59, 999));

      const dayLogs = logs.filter(log => 
        log.timestamp >= dayStart && log.timestamp <= dayEnd
      );

      const interactions = dayLogs.filter(log => 
        log.type === 'message_sent' || log.type === 'message_received'
      ).length;

      const documentsUploaded = dayLogs.filter(log => 
        log.type === 'document_upload_success'
      ).length;

      const conversationsStarted = dayLogs.filter(log => 
        log.type === 'message_sent'
      ).length; // Aproximação

      stats.push({
        date: dateStr,
        interactions,
        documents_uploaded: documentsUploaded,
        conversations_started: conversationsStarted
      });
    }

    return stats;
  }

  private getEmptyMetrics(): MonitoringMetrics {
    return {
      total_interactions: 0,
      total_documents: 0,
      total_conversations: 0,
      avg_response_time: 0,
      success_rate: 1,
      error_rate: 0,
      popular_queries: [],
      daily_stats: []
    };
  }

  // Função para obter estatísticas em tempo real
  async getRealTimeStats(): Promise<{
    activeUsers: number;
    messagesLastHour: number;
    systemStatus: 'healthy' | 'warning' | 'error';
    lastError?: string;
  }> {
    try {
      const logs = await databaseService.getInteractionLogs(100);
      const lastHour = new Date(Date.now() - 60 * 60 * 1000);

      const recentLogs = logs.filter(log => log.timestamp >= lastHour);
      const messagesLastHour = recentLogs.filter(log => 
        log.type === 'message_sent' || log.type === 'message_received'
      ).length;

      const errors = recentLogs.filter(log => log.type === 'error');
      const lastError = errors.length > 0 ? errors[0].metadata.error : undefined;

      let systemStatus: 'healthy' | 'warning' | 'error' = 'healthy';
      if (errors.length > 5) systemStatus = 'error';
      else if (errors.length > 2) systemStatus = 'warning';

      return {
        activeUsers: Math.ceil(messagesLastHour / 3), // Estimativa
        messagesLastHour,
        systemStatus,
        lastError
      };
    } catch (error) {
      console.error('Erro ao obter stats em tempo real:', error);
      return {
        activeUsers: 0,
        messagesLastHour: 0,
        systemStatus: 'error',
        lastError: 'Erro ao conectar com banco de dados'
      };
    }
  }

  // Função para exportar logs
  async exportLogs(startDate: Date, endDate: Date): Promise<InteractionLog[]> {
    try {
      const allLogs = await databaseService.getInteractionLogs(10000);
      return allLogs.filter(log => 
        log.timestamp >= startDate && log.timestamp <= endDate
      );
    } catch (error) {
      console.error('Erro ao exportar logs:', error);
      return [];
    }
  }

  // Função para limpar logs antigos
  async cleanOldLogs(daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
      // Implementar limpeza no databaseService se necessário
      console.log(`Limpeza de logs anteriores a ${cutoffDate.toISOString()}`);
      return 0; // Retornar número de logs removidos
    } catch (error) {
      console.error('Erro ao limpar logs:', error);
      return 0;
    }
  }
}

export const monitoringService = new MonitoringService();
