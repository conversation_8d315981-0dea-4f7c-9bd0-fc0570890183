import React from 'react';

interface SidebarProps {
  activeTab: 'chat' | 'documents' | 'monitoring' | 'whatsapp';
  onTabChange: (tab: 'chat' | 'documents' | 'monitoring' | 'whatsapp') => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ activeTab, onTabChange }) => {
  const tabs = [
    {
      id: 'chat' as const,
      name: 'Cha<PERSON>',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      )
    },
    {
      id: 'documents' as const,
      name: 'Documentos',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      )
    },
    {
      id: 'monitoring' as const,
      name: 'Monitoramento',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    },
    {
      id: 'whatsapp' as const,
      name: 'WhatsApp',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21l4-4 4 4M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
        </svg>
      )
    }
  ];

  return (
    <div className="fixed right-0 top-0 h-full w-64 z-40 flex flex-col bg-white border-l border-gray-200 shadow-lg">
      {/* Header da Sidebar */}
      <div className="p-6 border-b border-gray-200 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="w-12 h-12 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4 shadow-lg">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-800 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent text-center">
          Menu
        </h2>
        <p className="text-sm text-gray-600 mt-2 text-center">Navegue pelas funcionalidades</p>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-6">
        <div className="space-y-3">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange(tab.id)}
              className={`
                w-full flex items-center space-x-4 px-4 py-4 rounded-lg text-left transition-all duration-200 group
                ${activeTab === tab.id
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                }
              `}
            >
              {/* Ícone */}
              <div className={`transition-colors duration-200 ${
                activeTab === tab.id ? 'text-white' : 'text-gray-500 group-hover:text-blue-600'
              }`}>
                {tab.icon}
              </div>

              {/* Nome */}
              <span className={`
                font-medium transition-all duration-200
                ${activeTab === tab.id ? 'font-semibold text-white' : 'group-hover:font-semibold'}
              `}>
                {tab.name}
              </span>

              {/* Indicador ativo */}
              {activeTab === tab.id && (
                <div className="ml-auto w-2 h-2 bg-white rounded-full" />
              )}
            </button>
          ))}
        </div>
      </nav>

      {/* Footer da Sidebar */}
      <div className="p-6 border-t border-gray-200 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-3">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <div className="text-xs font-medium text-gray-600">Sistema Online</div>
          </div>
          <div className="text-base font-semibold text-gray-800 mb-1">
            Vereadora Rafaela de Nilda
          </div>
          <div className="text-xs text-gray-500 mb-3">
            Parnamirim/RN • 2025-2028
          </div>
          <div className="text-xs text-gray-600 bg-white rounded-lg p-2 border border-gray-200">
            💖 Servindo nossa cidade
          </div>
        </div>
      </div>
    </div>
  );
};
