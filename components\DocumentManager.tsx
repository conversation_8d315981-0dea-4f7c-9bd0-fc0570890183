import React, { useState } from 'react';
import { DocumentInput } from './DocumentInput';
import type { Document } from '../types';

interface DocumentManagerProps {
  documents: Document[];
  onDeleteDocument: (id: string) => void;
  onUpload: (file: File) => void;
}

export const DocumentManager: React.FC<DocumentManagerProps> = ({
  documents,
  onDeleteDocument,
  onUpload
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (date: Date | string | number) => {
    try {
      // Converter para Date se não for
      const dateObj = date instanceof Date ? date : new Date(date);

      // Verificar se é uma data válida
      if (isNaN(dateObj.getTime())) {
        return 'Data inválida';
      }

      return dateObj.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return 'Data inválida';
    }
  };

  const getStatusIcon = (status: Document['status']) => {
    switch (status) {
      case 'uploading':
        return (
          <div className="inline-flex items-center space-x-2 px-3 py-1 rounded-lg bg-blue-50 border border-blue-200">
            <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-blue-800 font-medium text-sm">Enviando</span>
          </div>
        );
      case 'processing':
        return (
          <div className="inline-flex items-center space-x-2 px-3 py-1 rounded-lg bg-yellow-50 border border-yellow-200">
            <div className="w-3 h-3 border-2 border-yellow-600 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-yellow-800 font-medium text-sm">Processando</span>
          </div>
        );
      case 'ready':
        return (
          <div className="inline-flex items-center space-x-2 px-3 py-1 rounded-lg bg-green-50 border border-green-200">
            <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span className="text-green-800 font-medium text-sm">Pronto</span>
          </div>
        );
      case 'error':
        return (
          <div className="inline-flex items-center space-x-2 px-3 py-1 rounded-lg bg-red-50 border border-red-200">
            <svg className="w-3 h-3 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-red-800 font-medium text-sm">Erro</span>
          </div>
        );
      default:
        return (
          <div className="inline-flex items-center space-x-2 px-3 py-1 rounded-lg bg-gray-50 border border-gray-200">
            <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span className="text-gray-800 font-medium text-sm">Desconhecido</span>
          </div>
        );
    }
  };

  const sortedDocuments = [...documents].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'date':
        try {
          const dateA = a.upload_date instanceof Date ? a.upload_date : new Date(a.upload_date);
          const dateB = b.upload_date instanceof Date ? b.upload_date : new Date(b.upload_date);
          comparison = dateA.getTime() - dateB.getTime();
        } catch (error) {
          comparison = 0;
        }
        break;
      case 'size':
        comparison = a.size - b.size;
        break;
    }

    return sortOrder === 'asc' ? comparison : -comparison;
  });

  const handleSort = (field: 'name' | 'date' | 'size') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('desc');
    }
  };

  const handleDelete = (id: string) => {
    if (showDeleteConfirm === id) {
      onDeleteDocument(id);
      setShowDeleteConfirm(null);
    } else {
      setShowDeleteConfirm(id);
    }
  };

  const getSortIcon = (field: 'name' | 'date' | 'size') => {
    if (sortBy !== field) {
      return (
        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
        </svg>
      );
    }
    
    return sortOrder === 'asc' ? (
      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
      </svg>
    ) : (
      <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4" />
      </svg>
    );
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 bg-white">
        <div className="max-w-5xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-800 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Gerenciamento de Documentos
              </h2>
              <p className="text-gray-600 text-lg mt-2">
                Gerencie os documentos da base de conhecimento
              </p>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
              <span className="text-blue-800 font-medium text-sm">
                {documents.length} documento{documents.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
          <DocumentInput onUpload={onUpload} />
        </div>
      </div>

      {/* Lista de documentos */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {documents.length === 0 ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center py-16 max-w-2xl">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6 shadow-lg">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-3xl font-bold text-gray-800 mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Nenhum documento carregado
              </h3>
              <p className="text-gray-600 text-lg leading-relaxed mb-8">
                Faça upload de documentos para começar a usar o sistema RAG e melhorar as respostas da IA.
              </p>
              <div className="max-w-md mx-auto">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-blue-800 font-medium text-sm">
                      Use o formulário acima para carregar documentos
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="max-w-5xl mx-auto px-4 py-6">
            {/* Header da tabela */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
                <div className="grid grid-cols-12 gap-4 text-base font-medium text-gray-800">
                  <button
                    onClick={() => handleSort('name')}
                    className="col-span-5 flex items-center space-x-2 hover:text-blue-600 transition-colors"
                  >
                    <span>Nome</span>
                    {getSortIcon('name')}
                  </button>

                  <div className="col-span-2 text-center">Status</div>

                  <button
                    onClick={() => handleSort('size')}
                    className="col-span-2 flex items-center justify-center space-x-2 hover:text-blue-600 transition-colors"
                  >
                    <span>Tamanho</span>
                    {getSortIcon('size')}
                  </button>

                  <button
                    onClick={() => handleSort('date')}
                    className="col-span-2 flex items-center justify-center space-x-2 hover:text-blue-600 transition-colors"
                  >
                    <span>Data</span>
                    {getSortIcon('date')}
                  </button>

                  <div className="col-span-1 text-center">Ações</div>
                </div>
              </div>

              {/* Lista de documentos */}
              <div className="divide-y divide-gray-200">
                {sortedDocuments.map((document) => (
                  <div
                    key={document.id}
                    className="px-6 py-5 hover:bg-gray-50 transition-colors"
                  >
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Nome */}
                      <div className="col-span-5">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm">
                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                          </div>
                          <div>
                            <p className="font-medium text-gray-800 truncate text-base">{document.name}</p>
                            {document.chunks_count && document.chunks_count > 0 && (
                              <p className="text-sm text-gray-600">{document.chunks_count} chunks processados</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Status */}
                      <div className="col-span-2 text-center">
                        {getStatusIcon(document.status)}
                      </div>

                      {/* Tamanho */}
                      <div className="col-span-2 text-center text-base text-gray-600">
                        {formatFileSize(document.size)}
                      </div>

                      {/* Data */}
                      <div className="col-span-2 text-center text-base text-gray-600">
                        {formatDate(document.upload_date)}
                      </div>

                      {/* Ações */}
                      <div className="col-span-1 text-center">
                        <button
                          onClick={() => handleDelete(document.id)}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            showDeleteConfirm === document.id
                              ? 'bg-red-500 text-white hover:bg-red-600'
                              : 'bg-gray-100 text-gray-600 hover:bg-red-50 hover:text-red-600'
                          }`}
                          title={showDeleteConfirm === document.id ? 'Clique novamente para confirmar' : 'Deletar documento'}
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Footer com estatísticas */}
      <div className="border-t border-gray-200 bg-white p-6">
        <div className="max-w-5xl mx-auto">
          <div className="flex justify-between items-center text-base text-gray-600">
            <span>
              {documents.length} documento{documents.length !== 1 ? 's' : ''} carregado{documents.length !== 1 ? 's' : ''}
            </span>
            <span>
              {formatFileSize(documents.reduce((total, doc) => total + doc.size, 0))} total
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
